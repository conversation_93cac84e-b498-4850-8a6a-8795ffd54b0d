<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="DebugRoslynSourceGenerator">AutoPatchGenerator/AutoPatchGenerator.csproj</projectFile>
    <projectFile>HarmonyPatchGenerator/HarmonyPatchGenerator.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="67dc5fea-a4d1-4f9f-bd95-626d449eb6bc" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/TestLE/ILRepack.targets" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/TestLE/Scripting/LuaAPI.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/TestLE/Scripting/LuaManager.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestLE/Routine/README_REFACTORING.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/TestLE/Test.cs" beforeDir="false" afterPath="$PROJECT_DIR$/TestLE/Test.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestLE/TestLE.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/TestLE/TestLE.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestLE/Utilities/PlayerHelpers.cs" beforeDir="false" afterPath="$PROJECT_DIR$/TestLE/Utilities/PlayerHelpers.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/TestLE" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;wrekklol&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/wrekklol/LEBot.git&quot;,
    &quot;accountId&quot;: &quot;4d8b2141-e745-430e-8fd7-9b90f1f24bd3&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/010ae88358374f86b90b8e86bb3e33c8662200/16/10a84b1f/List`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/2f4c1c47b4a64fa6a20f1036cf157e23f3a00/29/e00dacb2/TextMeshProUGUI.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/c085dc8e86384478a7a548c799c9b5f840800/12/5d0fdd7c/Harmony.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/00/2b4dd8cd/GroundPotionVisuals.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/03/8635512b/PickupAncientBonesInteraction.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/06/d79734b2/ItemData.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/1a/fc484dbf/NonItemPickupableGroundLabel`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/2c/19d84fad/GroundPotionLabel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/35/d39fe4d8/ActorSync.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/3e/335220d6/PickupGroundObjectInteraction.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/49/9edf12a3/GroundGoldLabel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/5f/a7253ad5/GroundAncientBoneVisuals.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/69/39aabb23/AncientBoneLabel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/ba/47b4f7ed/EchoWebIsland.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/bb/c6d7060b/PickupableGroundLabel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/c0/45190e6b/GroundItemLabel.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/df/49c79f27/GroundGoldVisuals.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/e7/1e94846f/GroundItemManager.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/ef/88fa86dc/LocalPlayer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ccd193d2cb0646b7ac515e5cf25aad86277fc00/f0/224b99bf/DroppableRewardType.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cf3da6e188c84dbe92a0a78ca4451a7f277f600/3b/35d8af14/MonolithPulse.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cf3da6e188c84dbe92a0a78ca4451a7f277f600/7d/98478abc/EchoLoadingStatusUI.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cf3da6e188c84dbe92a0a78ca4451a7f277f600/98/5126d7bd/MinimapFogOfWar.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cf3da6e188c84dbe92a0a78ca4451a7f277f600/b5/c1eed7ca/DungeonExitKeyProvider.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://D:/Projects/TestLE/TestLE/Globals.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://D:/Projects/TestLE/TestLE/Globals.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Patch.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Patches/Patch_ActorVisuals.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Patches/Patch_GroundGoldLabel.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Patches/Patch_MinimapFogOfWar.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Patches/Patch_MonolithPulse.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Test.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/TestLE/Types/AuctionHouse.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="Toolset" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="31ZA1OVo4FViREhQoGVqkjDmusP" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET Project.HarmonyPatchGenerator.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;RoslynSettingsId&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="67dc5fea-a4d1-4f9f-bd95-626d449eb6bc" name="Changes" comment="" />
      <created>1755721005433</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755721005433</updated>
      <workItem from="1755721005755" duration="3866000" />
      <workItem from="1755727397196" duration="32000" />
      <workItem from="1755769696430" duration="185000" />
      <workItem from="1755769955507" duration="6578000" />
      <workItem from="1755841711269" duration="649000" />
      <workItem from="1755864186062" duration="12321000" />
      <workItem from="1755929176026" duration="12191000" />
      <workItem from="1756020906674" duration="19508000" />
      <workItem from="1756098423139" duration="10834000" />
      <workItem from="1756112583661" duration="2572000" />
    </task>
    <task id="LOCAL-00001" summary=".">
      <option name="closed" value="true" />
      <created>1755808576738</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755808576738</updated>
    </task>
    <task id="LOCAL-00002" summary=".">
      <option name="closed" value="true" />
      <created>1755867168891</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755867168891</updated>
    </task>
    <task id="LOCAL-00003" summary=".">
      <option name="closed" value="true" />
      <created>1755892632997</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755892632997</updated>
    </task>
    <task id="LOCAL-00004" summary=".">
      <option name="closed" value="true" />
      <created>1755893877507</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755893877507</updated>
    </task>
    <task id="LOCAL-00005" summary=".">
      <option name="closed" value="true" />
      <created>1755894797615</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755894797615</updated>
    </task>
    <task id="LOCAL-00006" summary=".">
      <option name="closed" value="true" />
      <created>1755930369122</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755930369122</updated>
    </task>
    <task id="LOCAL-00007" summary=".">
      <option name="closed" value="true" />
      <created>1755930898199</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755930898199</updated>
    </task>
    <task id="LOCAL-00008" summary=".">
      <option name="closed" value="true" />
      <created>1755931693227</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755931693227</updated>
    </task>
    <task id="LOCAL-00009" summary=".">
      <option name="closed" value="true" />
      <created>1755937430414</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755937430414</updated>
    </task>
    <task id="LOCAL-00010" summary=".">
      <option name="closed" value="true" />
      <created>1756039829525</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1756039829525</updated>
    </task>
    <task id="LOCAL-00011" summary=".">
      <option name="closed" value="true" />
      <created>1756041254627</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1756041254627</updated>
    </task>
    <task id="LOCAL-00012" summary=".">
      <option name="closed" value="true" />
      <created>1756064725809</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1756064725809</updated>
    </task>
    <task id="LOCAL-00013" summary=".">
      <option name="closed" value="true" />
      <created>1756065076230</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1756065076230</updated>
    </task>
    <task id="LOCAL-00014" summary=".">
      <option name="closed" value="true" />
      <created>1756069546890</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1756069546890</updated>
    </task>
    <task id="LOCAL-00015" summary=".">
      <option name="closed" value="true" />
      <created>1756103008709</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1756103008709</updated>
    </task>
    <task id="LOCAL-00016" summary=".">
      <option name="closed" value="true" />
      <created>1756103170323</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1756103170323</updated>
    </task>
    <task id="LOCAL-00017" summary=".">
      <option name="closed" value="true" />
      <created>1756108745908</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1756108745908</updated>
    </task>
    <task id="LOCAL-00018" summary=".">
      <option name="closed" value="true" />
      <created>1756110277533</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1756110277533</updated>
    </task>
    <option name="localTasksCounter" value="19" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="." />
    <option name="LAST_COMMIT_MESSAGE" value="." />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>